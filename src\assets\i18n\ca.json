﻿{
	"MODULE_TRACKING": {
		"XLSX_ERROR": "No es pot generar l'excel en aquests moments, intenta-ho més tard.",
		"ROLES": {
			"ADMIN": "Administrador",
			"TECHNICIAN": "Tècnic",
			"TECHUSER": "Tècnic",
			"READ_ONLY": "Només lectura",
			"CIUTADA": "Ciutadà",
			"INSPECTOR": "Inspector"
		},
		"CERTIFICATES": {
			"0": "Persona física",
			"1": "Persona jurídica",
			"2": "Component SSL",
			"3": "Seu electronica",
			"4": "Segell electronic",
			"5": "Empleat públic",
			"6": "Empleat personalitat juridica",
			"7": "Empleat públic Pseudonim",
			"8": "Qualificat segell",
			"9": "Qualificat autentificacio",
			"10": "Certificat segell temp",
			"11": "Representant de persona jurídica",
			"12": "Representant entitat juridica"
		},
		"LOGIN_METHODS": {
			"CERTIFICAT": "Certificat",
			"IDCATMOBIL": "idCAT Mòbil",
			"GICAR": "Gicar",
			"CLAVE": "Cl@ve"
		},
		"NAVBAR": {
			"LAST_SEEN": "Última conexió: ",
			"ROLE": "Rol",
			"SELF_ASSESSMENT": "Autoliquidacions (Antigues)",
			"SELF_ASSESSMENT_V2": "Nous Tributs",
			"DRAFTS": "Sessions de treball",
			"USER": "Usuaris",
			"PAYMENT_SETTLEMENT": "Liquidacions",
			"TEARC_MONITOR": "Resolucions",
			"SIMULATED_LOGIN": "Login simulat",
			"MANAGEMENT": "Gestions",
			"REFUND": "Devolució d'ingresos indeguts",
			"NOTIFICATIONS": "Configuració d'avisos",
			"SARCAT": "S@RCAT",
			"AUTOLIQUIDACIONS": "Autoliquidacions",
			"GESTIONS": "Gestions",
			"PAGAMENTS": "Pagaments",
			"NIF_ENCRYPTION": "Encriptació nif",
			"TAXES": "Tributs",
			"MANAGEMENTS_MONITOR": "Gestions",
			"BIOMETRIC_SIGNATURE": "Signatura Biomètrica",
			"USERS": "Usuaris",
			"TOOLS": "Eines",
			"AREA_PRIVADA": "El meu espai ATC",
			"MAINTENANCE_CONF_SEU2": "Configuració manteniment SEU2",
			"MAINTENANCE_CONF_SEU": "Configuració manteniment SEU",
			"EXPEDIENTES": "Expedients AEAT"
		},
		"PAYMENT_METHODS": {
			"BANK_ACCOUNT": "Càrrec a compte",
			"CREDIT_CARD": "Targeta",
			"BIZUM": "Bizum"
		},
		"TYPES": {
			"DECINF": "DECINF",
			"TRIBUT": "TRIBUT"
		},
		"STATES": {
			"HEADER": "Estat",
			"GENERATED": "Generat",
			"RECIVED": "Rebut",
			"ERROR": "Error",
			"DRAFT": "Esborrany",
			"DRAFT_REVALIDATE": "Re-validar",
			"DRAFT_VALIDATING": "Validant esborrany",
			"DRAFT_VALIDATED": "Esborrany validat",
			"DRAFT_ERROR": "Esborrany amb errors",
			"DRAFT_GROUPING": "Esborrany agrupant",
			"DRAFT_GROUPING_ERROR": "Error agrupació",
			"DRAFT_GROUPED": "Esborrany agrupat",
			"RECEIVED": "Rebut",
			"VALIDATING": "Validant",
			"VALIDATED": "Validat",
			"VALIDATING_ERROR": "Error validació",
			"PAYING": "Pagant",
			"PAYING_ERROR": "Error pagament",
			"CONFIRMED": "Confirmat",
			"CONFIRMED_ERROR": "Error confirmació",
			"NOTIFIED_ERROR": "Error notificació",
			"NOTIFYING": "Notificant pagament",
			"PAID": "Pagat",
			"CONSOLIDATING": "Consolidant",
			"CONSOLIDATING_ERROR": "Error de consolidació",
			"CONSOLIDATED": "Consolidat",
			"PRESENTING": "Presentant",
			"PRESENTING_ERROR": "Error de presentació",
			"PROCESSING_ERROR": "Pagat, error de presentació",
			"PRESENTED": "Presentat",
			"PROCESSING": "Presentant",
			"NO_PRESENTED": "No presentat",
			"PROCESSED": "Pagat i presentat",
			"TRAMITATED": "Consolidat",
			"TRAMITATED_ERROR": "Error de consolidació",
			"TRAMITATING": "Consolidant"
		},
		"WARNING_MSG": {
			"TITLE": "No és possible eliminar aquesta autoliquidació",
			"SUBTITLE": "Les autoliquidacions amb l'estat {{state}} no es poden eliminar"
		},
		"COMPONENT_LIQUIDACIO": {
			"PAGE_TITLE": "Dades Autoliquidació",
			"MENU_TITLE": "PORTAL TRACKING",
			"BUTTONS": {
				"DETAIL": "Veure detall",
				"EXCEL": "Llista d'autoliquidacions",
				"PAYMENT_DETAIL": "Veure dades del pagament",
				"RECORD_DETAIL": "Veure dades de l'expedient",
				"RETRY_PAYMENT": "Reintentar pagament",
				"RETRY_PRESENTATION": "Reintentar presentació",
				"RETRY_NOTIFICATION": "Reintentar notificació",
				"DELETE_SELF_ASSESSMENT": "Eliminar autoliquidació",
				"CLEAR_SELF_ASSESSMENT_ERRORS": "Netejar errors autoliquidació",
				"ACTION1": "Notificar Pagament GT",
				"ACTION2": "Veure document pare",
				"ACTION3": "Veure document de diligència",
				"ACTION4": "Veure document de notificació",
				"OPTIONS": "Opcions"
			},
			"CONFIRMATION_MESSAGES": {
				"DELETE_SELF_ASSESSMENT_RECEIPT": "¿Estàs segur d'eliminar l'autoliquidació amb nº Justificant {{numJustificant}}?",
				"DELETE_SELF_ASSESSMENT_NO_RECEIPT": "¿Estàs segur d'eliminar l'autoliquidació?",
				"CLEAR_SELF_ASSESSMENT_ERRORS": "¿Estàs segur de netejar els errors de l'autoliquidació amb nº Justificant {{numJustificant}}?"
			},
			"TABLE": {
				"TRUE": "Sí",
				"FALSE": "No",
				"ORIGIN": "Origen",
				"COMUNICATION_ID": "ID Comunicació",
				"RECEIPT": "Justificant",
				"AMOUNT": "Import",
				"PAYMENT_METHOD": "Mètode de pagament",
				"FILE": "Fitxer",
				"CURRENT_STATE": "Estat Actual",
				"PRES_STATE": "Estat Presentació",
				"PRESENTATOR_NAME": "Nom Presentador",
				"PRESENTATOR_NIF": "NIF Presentador",
				"TITULAR_NAME": "Nom Titular",
				"TITULAR_NIF": "NIF Titular",
				"SP_NAME": "Nom SP",
				"SP_NIF": "NIF SP",
				"PROTOCOL": "Protocol",
				"NOTARY_NAME": "Nom notari",
				"MFPT_ID": "Id MFPT",
				"MODEL": "Model",
				"COMPLEMENTARY": "Complementària",
				"DATE": "Data",
				"MUI_REFERENCE": "Referencia MUI",
				"DETAIL": "Detall",
				"PAYMENT_DETAIL": "Pagament",
				"RECORD_DETAIL": "Expedient",
				"MUI": "MUI",
				"ID_TRAMITACIO": "ID Tramitació",
				"ACTIONS": "Accions"
			},
			"DETAIL_MODAL": {
				"FIELDS": {
					"idSelfAssessment": "ID Autoliquidació",
					"amount": "Import",
					"loadDate": "Data de càrrega",
					"idReceipt": "Número justificant",
					"model": "Model",
					"state": "Estat",
					"idTramitacio": "ID Tramitació",
					"notarialFileReference": "Número referència"
				},
				"TITLE": "Detall de l'autoliquidació",
				"MODEL_ID": "ID Model",
				"ORIGIN": "Origen",
				"PRESENTATION_REQUEST_ID": "ID Petició presentació",
				"ERRORS": "Errors",
				"STATE_CHANGES": "Historial d' estats",
				"DOCUMENTS": "Documents",
				"CHECK_DECLARATION": "Veure declaració",
				"CLOSE": "Tancar",
				"FILTER": "Filtre",
				"NAVIGATION_TITLE": "Justificant: {{numJustificant}}",
				"PAYMENT_DETAIL": "Pagaments",
				"TAB_TITLE_PRESENTATIONS": "Presentacions",
				"TAB_TITLE_VALIDATIONS": "Validacions",
				"TAB_TITLE_NOTIFICATIONS": "Notificacions",
				"TAB_TITLE_GROUPING": "Agrupacions",
				"TAB_TITLE_TRAMITACIONS": "Tramitacions"
			},
			"DETAIL_MODAL_TAB": {
				"DATE": "Data",
				"DESCRIPTION": "Descripció",
				"DOC_MANAGER_REF": "Referencia Gestor Documental",
				"PRESENTATION_REQUEST": "Petició de presentació",
				"DECLARATION": "Declaració",
				"BEFORE": "Estat anterior",
				"CURRENT": "Estat actual"
			},
			"SECTION_SEARCH": {
				"SECTION_TITLE": "Filtres de cerca",
				"FORM": {
					"FIELDS": {
						"loadDateFrom": "Data inici",
						"loadDateTo": "Data final",
						"nivel": "Nivell",
						"modulo": "Mòdul",
						"origen": "Origen",
						"state": "Estat actual",
						"idReceipt": "Justificant",
						"idC": "ID Comunicació",
						"idP": "ID Petició",
						"nifSP": "NIF SP",
						"nifPresenter": "NIF Presentador",
						"nifTitular": "NIF Titular",
						"notario": "Nom notari",
						"protocolo": "Protocol",
						"model": "Model",
						"paymentMethod": "Mètode de pagament",
						"errores": "Només erronis",
						"SPRIU": "Només pendent e-SPRIU",
						"clauCobramentMUIFilter": "Clau cobrament MUI",
						"presentationState": "Estat Presentació",
						"idTramitacio": "ID Tramitació",
						"idPRPT": "ID Agrupació",
						"optionsFilter": "Opcions",
						"complementedNumJustificant": "És complementària"
					}
				}
			},
			"SECTION_RESULTS": {
				"SECTION_TITLE": "Resultats de la cerca",
				"MODAL_TITLE": "Dades de l'autoliquidació",
				"MODAL_SUBTITLE": "ID autoliquidació: {{idLiquidacion}}",
				"MODAL1_TITLE": "Detall del pagament",
				"MODAL1_SUBTITLE": "Dades del pagament",
				"MODAL2_TITLE": "Detall de l'expedient",
				"MODAL2_SUBTITLE1": "Dades de l'expedient",
				"MODAL2_SUBTITLE2": "Dades de la notificació",
				"COL1": "NRC",
				"COL2": "Data Pagament",
				"COL3": "Dades Bancaries",
				"COL4": "PRPTID GT",
				"COL5": "Num Expedient",
				"COL6": "CSV",
				"COL7": "Data Presentació",
				"COL8": "MUI",
				"COL9": "Data Notificació",
				"COL10": "URL Notificació",
				"COL11": "Referència Gestor Documental",
				"COL12": "Reintents"
			}
		},
		"COMPONENT_GESTIONS": {
			"PAGE_TITLE": "Gestions",
			"PAYMENTS_PAGE_TITLE": "Dades Pagaments",
			"PAYMENTS_DETAIL_TITLE": "Detall del pagament",
			"COMPONENT_NIF_ENCRYPTION": {
				"PAGE_TITLE": "Encriptació de nif",
				"NIF": "NIF",
				"ENCRYPTED_NIF": "NIF encriptat",
				"BUTTONS": {
					"ENCRYPT": "Encriptar",
					"DECRYPT": "Desencriptar"
				}
			},
			"COMPONENT_MAINTENANCE_CONF": {
				"PAGE_TITLE": "Configuració manteniment",
				"BUTTONS": {
					"CREATE": "Nova configuració",
					"UPDATE": "Modificar configuració",
					"DELETE": "Esborrar configuración"
				},
				"STATES": {
					"ACTIVE": "Actiu",
					"IN_MAINTENANCE": "En manteniment",
					"SCHEDULED_MAINTENANCE": "Manteniment programat"
				},
				"TABLE": {
					"COLUMNS": {
						"application": "Funcionalitat",
						"stateRow": "Estat",
						"maintenanceStart": "Data d'inici",
						"maintenanceEnd": "Data de fi",
						"nouCapcalera": "Nou capcalera"
					}
				},
				"CONTEXT_OPTIONS": {
					"EL_MEU_ESPAI_ATC": "El meu espai atc",
					"CITA_PREVIA": "Cita previa",
					"ESTADES_TURISTIQUES": "IEET - Estades turistiques (940,950,920)",
					"TRANSMISSIONS_PATRIMONIALS": "ITP - Esborrany transmissions patrimonials (600)",
					"SIGNATURA_INSPECCIO": "Signatura inspeccio",
					"APORTAR_DOCUMENTACIO": "Aportar documentació",
					"DEVOLUCIO_INGRESSOS": "Devolucio ingressos indeguts",
					"PAGAMENT_LIQUIDACIONS": "Pagament liquidacions",
					"RECURS_REPOSICIO": "Recursos de reposicio",
					"DADES_CONTACTE": "Dades contacte",
					"RECURS": "Recurs",
					"ANTENES": "IIIMA - Instal·lacions Medi Ambient (550, 560)",
					"CSV": "Consulta CSV",
					"ITPAJ": "ITP - Transmissions Patrimonials (600)",
					"IGEC": "IGEC - Grans Establiments (910)",
					"ISBEE": "IBEE - Begudes Ensucrades (520)",
					"IANP": "IANP - Actius No Productius (540)",
					"GRANS_ESTABLIMENTS": "IGEC - Grans Establiments (910)",
					"DECLARACIONS_INFORMATIVES": "DECINF - Declaracions informatives",
					"SEGURETAT": "Seguretat",
					"ACTIUS_NP": "IANP - Actius No Productius (540)",
					"ASSEGURANCES": "ISD - Assegurances De Vida (652)",
					"SIMULADORS": "Simuladors",
					"DONACIONS": "ISD - Donacions (651)",
					"BEGUDES_ENSUCRADES": "IBEE - Begudes Ensucrades (520)",
					"PADRO_CO2": "IEDC - Emissions C02 vehicles",
					"APOSTES": "JOC - Apostes (042)",
					"REA": "Reclamacions economico administratives",
					"ALLEGACIONS": "Al·legacions",
					"APP": "Aplicacio mobil per al contribuent de l'ATC",
					"GASOS": "IEGI - Emissio Gasos Industria (980)",
					"AVIACIO": "IENA - Emissio Gasos Aviacio (990)",
					"SIGNATURA_INSPECCIO_V2": "Signatura inspecció",
					"DECINF": "DECINF - Declaracions informatives"
				},
				"MODAL": {
					"MODES": {
						"FILTER": "Filtres de cerca",
						"CREATE": "Crear configuración",
						"UPDATE": "Actualitzar configuración",
						"DELETE": "Esborrar configuración"
					},
					"DELETE_SUBTITLE": "Està segur que desitja esborrar l'element sel·leccionat?"
				}
			},
			"COMPONENT_CASES":{
				"LIST": {
					"PAGE_TITLE": "Expedients"
				},
				"DETAIL": {
					"PAGE_TITLE": "Detall de l'expedient"
				}
			},
			"COMPONENT_SARCAR": {
				"PAGE_TITLE": "S@RCAT"
			}
		},
		"COMPONENT_SIMULATED_LOGIN": {
			"PAGE_TITLE": "Login simulat",
			"FIELDS": {
				"METHOD": "Tipus de login",
				"SIMULATION_DATE": "Simulació de data del sistema",
				"CERTIFICATE": "Tipus de certificat",
				"USER_NIF": "NIF de l'usuari",
				"COMPANY_NIF": "NIF de la companyia"
			}
		},
		"COMPONENT_AREA_PRIVADA": {
			"PAGE_TITLE": "El meu espai ATC",
			"COMPONENT_AUTOLIQUIDACIONS": {
				"PAGE_TITLE": "Autoliquidacions El meu espai ATC"
			},
			"COMPONENT_GESTIONS": {
				"PAGE_TITLE": "Gestions El meu espai ATC"
			},
			"COMPONENT_PAGAMENTS": {
				"PAGE_TITLE": "Pagaments El meu espai ATC"
			},
			"COMPONENT_NOTIFICATIONS": {
				"PAGE_TITLE": "Configuració d'avisos",
				"NOTIFICATION_OPTIONS": {
					"ERROR": "Error",
					"WARNING": "Advertència",
					"INFO": "Info",
					"SUCCESS": "Èxit",
					"CAD": "Caducat"
				},
				"CONTEXT_OPTIONS": {
					"AREA_PRIVADA": "El meu espai ATC"
				},
				"STATE_OPTIONS": {
					"ACTIVE": "Actiu",
					"INACTIVE": "Inactiu"
				},
				"NOTIFICATION_MODAL": {
					"FILTER": "Filtres de cerca",
					"NEW": "Nou avís",
					"EDIT": "Editar avís",
					"VALIDATIONS": {
						"LESS_EQUAL": "El camp a validar ha de ser inferior o igual al camp Data de fi.",
						"GREATER_EQUAL": "El camp a validar ha de ser major o igual al camp Data d'inici."
					}
				},
				"DELETE_MODAL": {
					"TITLE": "Eliminar",
					"MSG": "Està segur que desitja eliminar l'element sel·leccionat?"
				},
				"TABLE": {
					"NEW_NOTIFICATION": "Nou avís",
					"COLUMNS": {
						"NOTIFICATION_TYPE": "Tipus d'avís",
						"TITLE_CA": "Títol en català",
						"TITLE_ES": "Títol en espanyol",
						"MSG_CA": "Missatge en català",
						"MSG_ES": "Missatge en espanyol",
						"CONTEXT": "Context",
						"INITIAL_DATE": "Data d'inici",
						"FINAL_DATE": "Data de fi",
						"STATE": "Estat"
					}
				}
			}
		},
		"MODULE_SELFASSESSMENT_V2": {
			"PAGE_TITLE": "Dades Tribut",
			"STATES": {
				"GENERAT": "Generat",
				"NO_PRESENTAT": "No presentat",
				"PRESENTANT": "Presentant",
				"PRESENTACIO_ERROR": "Error Presentació",
				"PRESENTAT": "Presentat",
				"ESBORRANY": "Esborrany",
				"ESBORRANY_ERROR": "Error Esborrany",
				"ESBORRANY_VALIDAT": "Esborrany validat",
				"ESBORRANY_VALIDANT": "Esborrany validant",
				"ESBORRANY_AGRUPANT": "Esborrany agrupant",
				"ESBORRANY_AGRUPAT": "Esborrany agrupat",
				"PAGAT": "Presentat i pagat",
				"PAGANT": "Pagant",
				"PENDENT_PAGAMENT": "Pagament Pendent",
				"PAGAMENT_ERROR": "Error Pagament",
				"PAGAMENT_CANCELLAT": "Pagament cancel·lat",
				"NOTIFICANT_PAGAMENT": "Notificant pagament",
				"NOTIFICACIO_ERROR": "Notificació error",
				"PAGAMENT_NOTIFICAT": "Pagament notificat",
				"TRAMITAT": "Tramitat",
				"TRAMITANT": "Tramitant",
				"TRAMITACIO_ERROR": "Error Tramitació",
				"CONSOLIDANT": "Consolidant",
				"CONSOLIDAT": "Consolidat",
				"CONSOLIDACIO_ERROR": "Error consolidació",
				"ERROR": "Error",
				"PENDENT_PRESENTACIO": "Pendent presentació",
				"ESBORRANY_AGRUPANT_ERROR": "Esborrany agrupant error",
				"DELETE": "Esborrat"
			},
			"TABLE": {
				"EMPTY_MSG": "Cap resultat coincideix amb els vostres criteris de cerca. Torneu-ho a provar.",
				"COLUMNS": {
					"numJustificant": "Núm. Justificant",
					"estat": "Estat",
					"impost": "Impost",
					"tipus": "Tipus",
					"nomPresentador": "Nom presentador",
					"nifPresentador": "NIF presentador",
					"nomSubjectePassiu": "Nom SP",
					"nifSubjectePassiu": "NIF SP",
					"nomTitular": "Nom titular",
					"nifTitular": "NIF titular",
					"model": "Model",
					"dataAlta": "Data",
					"mui": "Referencia ESPRIU",
					"dataModificacio": "Data",
					"pagamentEstat": "Estat pagament",
					"presentacioEstat": "Estat presentació",
					"idTramitacioPagament": "ID pagament",
					"idTramitacio": "ID presentació",
					"totalIngressar": "Import",
					"idAgrupacio": "ID agrupació"
				},
				"FILTER": {
					"TITLE": "Filtres de cerca",
					"FIELDS": {
						"estat": "Estat",
						"tipus": "Tipus",
						"impost": "Impost",
						"nifPresentador": "NIF Presentador",
						"nifSubjectePassiu": "NIF SP",
						"nifTitular": "NIF Titular",
						"numJustificant": "Número Justificant",
						"model": "Model",
						"dateFromForm": "Data inici",
						"dateToForm": "Data final",
						"pagamentEstat": "Estat de pagament",
						"presentacioEstat": "Estat de presentació",
						"idTramitacioPagament": "ID tramitació de pagament",
						"idTramitacio": "ID tramitació de presentació",
						"errorsFilter": "Opcions",
						"errors": "Només erronis",
						"haveComplementari": "És complementària",
						"tipusPagament": "Mètode de pagament",
						"idAgrupacio": "ID agrupació",
						"mui": "Referència cobrament E-SPRIU",
						"types": "Tipus"
					}
				}
			},
			"DETAIL": {
				"PAGE_TITLE": "Detall del tribut",
				"CARD": {
					"TITLE": "Dades del tribut",
					"FIELDS": {
						"estat": "Estat",
						"impost": "Impost",
						"tipus": "Tipus",
						"nomPresentador": "Nom Presentador",
						"nifPresentador": "NIF Presentador",
						"nomSubjectePassiu": "Nom SP",
						"nifSubjectePassiu": "NIF SP",
						"model": "Model",
						"dataAlta": "Data Alta",
						"dataModificacio": "Data Modificació",
						"idAutoliquidacio": "ID Autoliquidació",
						"numJustificant": "Número Justificant",
						"periode": "Periode",
						"exercici": "Exercici",
						"idAgrupacio": "ID agrupació"
					}
				},
				"STATE_HISTORY_TABLE": {
					"TITLE": "Historial d' estats",
					"COLUMNS": {
						"date": "Data",
						"state": "Estat"
					}
				},
				"TABS": {
					"DOCUMENTS": "Documents",
					"PAYMENTS": "Pagaments",
					"PRESENTATIONS": "Presentacions",
					"TRAMITACIONS": "Tramitacions",
					"VALIDATIONS": "Validacions",
					"GROUPING": "Agrupacions",
					"NOTIFICATIONS": "Notificacions",
					"ERRORS": "Errors",
					"INTERVENERS": "Intervinents",
					"COMPLEMENTARY": "Complementària"
				}
			}
		},
		"MODULE_DRAFTS": {
			"PAGE_TITLE": "Dades sessions de treball",
			"ALERT": {
				"COPY_TITLE": "S'ha fet una còpia de la sessió de treball correctament",
				"DELETE_TITLE": "S'ha esborrat la còpia de la sessió de treball correctament"
			},
			"STATES": {
				"GENERAT": "Generat",
				"NO_PRESENTAT": "No presentat",
				"PRESENTANT": "Presentant",
				"PRESENTACIO_ERROR": "Error Presentació",
				"PRESENTAT": "Presentat",
				"ESBORRANY": "Esborrany",
				"ESBORRANY_ERROR": "Error Esborrany",
				"ESBORRANY_VALIDAT": "Esborrany validat",
				"ESBORRANY_VALIDANT": "Esborrany validant",
				"ESBORRANY_AGRUPANT": "Esborrany agrupant",
				"ESBORRANY_AGRUPAT": "Esborrany agrupat",
				"PAGAT": "Presentat i pagat",
				"PAGANT": "Pagant",
				"PENDENT_PAGAMENT": "Pagament Pendent",
				"PAGAMENT_ERROR": "Error Pagament",
				"PAGAMENT_CANCELLAT": "Pagament cancel·lat",
				"NOTIFICANT_PAGAMENT": "Notificant pagament",
				"NOTIFICACIO_ERROR": "Notificació error",
				"PAGAMENT_NOTIFICAT": "Pagament notificat",
				"TRAMITAT": "Tramitat",
				"TRAMITANT": "Tramitant",
				"TRAMITACIO_ERROR": "Error Tramitació",
				"CONSOLIDANT": "Consolidant",
				"CONSOLIDAT": "Consolidat",
				"CONSOLIDACIO_ERROR": "Error consolidació",
				"ERROR": "Error",
				"PENDENT_PRESENTACIO": "Pendent presentació",
				"ESBORRANY_AGRUPANT_ERROR": "Esborrany agrupant error",
				"DELETE": "Esborrat"
			},
			"TABLE": {
				"EMPTY_MSG": "Cap resultat coincideix amb els vostres criteris de cerca. Torneu-ho a provar.",
				"COLUMNS": {
					"id": "ID",
					"esborrany": "Esborrany",
					"numJustificant": "Núm. Justificant",
					"estat": "Estat",
					"impost": "Impost",
					"tipus": "Tipus",
					"nomPresentador": "Nom presentador",
					"nifPresentador": "NIF presentador",
					"nomSubjectePassiu": "Nom SP",
					"nifSubjectePassiu": "NIF SP",
					"nomTitular": "Nom titular",
					"nifTitular": "NIF titular",
					"model": "Model",
					"dataAlta": "Data",
					"dataModificacio": "Data",
					"pagamentEstat": "Estat pagament",
					"presentacioEstat": "Estat presentació",
					"idTramitacioPagament": "ID pagament",
					"idTramitacio": "ID presentació",
					"totalIngressar": "Import",
					"indEsborrany": "Esborrany",
					"indErrEsborrany": "Error",
					"indCopia": "Còpia"
				},
				"FILTER": {
					"TITLE": "Filtres de cerca",
					"FIELDS": {
						"indCopia": "Només còpies",
						"esborrany": "Esborrany",
						"esborranyLabel": "Opcions",
						"estat": "Estat",
						"tipus": "Tipus",
						"impost": "Impost",
						"nifPresentador": "NIF Presentador",
						"nifSubjectePassiu": "NIF SP",
						"nifTitular": "NIF Titular",
						"numJustificant": "Número Justificant",
						"model": "Model",
						"dateFromForm": "Data inici",
						"dateToForm": "Data final",
						"pagamentEstat": "Estat de pagament",
						"presentacioEstat": "Estat de presentació",
						"idTramitacioPagament": "ID tramitació de pagament",
						"idTramitacio": "ID tramitació de presentació",
						"errorsFilter": "Opcions",
						"errors": "Només erronis",
						"errEsborrany": "Error",
						"haveComplementari": "És complementària",
						"tipusPagament": "Mètode de pagament"
					}
				}
			},
			"DETAIL": {
				"PAGE_TITLE": "Detall de la sessió de treball",
				"DELETE_MODAL": {
					"TITLE": "Eliminar sessió de treball",
					"TITLE_ERROR": "No es pot eliminar la sessió de treball",
					"SUBTITLE": "¿Esteu segur d'eliminar la sessió de treball amb l'id {{id}}?",
					"SUBTITLE_ERROR": "La sessió de treball que voleu esborrar no és una còpia"
				},
				"COPY_MODAL": {
					"TITLE_ERROR": "No es pot copiar la sessió de treball",
					"SUBTITLE_ERROR": "No es pot copiar una còpia d'una sessió de treball"
				},
				"CARD": {
					"TITLE": "Dades de la sessió de treball",
					"FIELDS": {
						"id": "ID Sessió de treball",
						"estat": "Estat",
						"impost": "Impost",
						"idioma": "Idioma",
						"tipus": "Tipus",
						"nomPresentador": "Nom Presentador",
						"nifPresentador": "NIF Presentador",
						"nomSubjectePassiu": "Nom SP",
						"nifSubjectePassiu": "NIF SP",
						"nifTitular": "NIF Titular",
						"nomTitular": "Nom titular",
						"model": "Model",
						"dataAlta": "Data Alta",
						"dataModificacio": "Data Modificació",
						"idAutoliquidacio": "ID Autoliquidació",
						"numJustificant": "Número Justificant",
						"periode": "Periode",
						"exercici": "Exercici",
						"esborrany": "Esborrany",
						"dadesAddicionals": "Dades Addicionals"
					}
				},
				"STATE_HISTORY_TABLE": {
					"TITLE": "Historial d' estats",
					"COLUMNS": {
						"date": "Data",
						"state": "Estat"
					}
				},
				"TABS": {
					"DOCUMENTS": "Documents",
					"PAYMENTS": "Pagaments",
					"PRESENTATIONS": "Presentacions",
					"TRAMITACIONS": "Tramitacions",
					"VALIDATIONS": "Validacions",
					"GROUPING": "Agrupacions",
					"NOTIFICATIONS": "Notificacions",
					"ERRORS": "Errors",
					"SELFASSESSMENT": "Autoliquidacions",
					"INTERVENERS": "Intervinents",
					"COMPLEMENTARY": "Complementària"
				},
				"BUTTONS": {
					"COPY_DRAFT": "Copiar sessió de treball",
					"DELETE_DRAFT": "Eliminar sessió de treball"
				}
			}
		},
		"MODULE_MANAGEMENTS_MONITOR": {
			"PAGE_TITLE": "Dades Gestions",
			"STATES": {
				"GENERAT": "Generat",
				"NO_PRESENTAT": "No presentat",
				"PRESENTANT": "Presentant",
				"PRESENTACIO_ERROR": "Error Presentació",
				"PRESENTAT": "Presentat",
				"ESBORRANY": "Esborrany",
				"PAGAT": "Pagat",
				"PAGANT": "Pagant",
				"PENDENT_PAGAMENT": "Pagament Pendent",
				"PAGAMENT_ERROR": "Error Pagament",
				"TRAMITAT": "Tramitat",
				"TRAMITANT": "Tramitant",
				"TRAMITACIO_ERROR": "Error Tramitació",
				"CONSOLIDANT": "Consolidant",
				"CONSOLIDAT": "Consolidat",
				"CONSOLIDACIO_ERROR": "Error consolidació",
				"ERROR": "Error"
			},
			"TABLE": {
				"EMPTY_MSG": "Cap resultat coincideix amb els vostres criteris de cerca. Torneu-ho a provar.",
				"COLUMNS": {
					"idTramitacio": "ID Tramitació",
					"estat": "Estat",
					"origen": "Origen",
					"presentadorNom": "Nom Presentador",
					"presentadorNif": "NIF Presentador",
					"subjectePassiuNom": "Nom SP",
					"subjectePassiuNif": "NIF SP",
					"tramit": "Tipus",
					"dadesAddicionals": "Dades Addicionals",
					"dataModificacio": "Data"
				},
				"ROWS": {
					"TRAMIT": {
						"AVISOS": "Avisos",
						"IIIMA": "IIIMA",
						"ITPAJ": "ITPAJ",
						"IGEC": "IGEC",
						"DONACIONS": "Donacions",
						"IANP": "IANP",
						"ISBEE": "ISBEE",
						"ASSEGURANCES": "Assegurances",
						"RECURS": "Recurs",
						"RECURS_REPOSICIO_CO2": "Recurs CO2",
						"REA_CO2": "REA CO2",
						"REA": "REA",
						"DOMICILIACIO": "Domiciliació",
						"ALLEGACIO_CO2": "Al·legacions CO2",
						"EXPEDIENT_CSV": "Expedient CSV",
						"CANVI_ADRECA": "Canvi Adreça",
						"ALTA_NE": "Alta not. electròniques",
						"MOD_NE": "Mod not. electròniques",
						"DEVOLUCIO_INGRESSOS": "Devolució Ingressos",
						"SIGNATURA_INSPECCIO": "Signatura Inspecció",
						"APORTAR_DOCUMENTACIO": "Aportar documentació",
						"AJORNAMENT_FRACCIONAMENT": "Ajornament/Fraccionament"
					}
				},
				"FILTER": {
					"TITLE": "Filtres de cerca",
					"FIELDS": {
						"state": "Estat",
						"presentadorNif": "NIF Presentador",
						"subjectePassiuNif": "NIF SP",
						"dateFromForm": "Data inici",
						"dateToForm": "Data final",
						"tipus": "Tipus",
						"dadesAddicionals": "Dades Addicionals",
						"origen": "Origen",
						"idTramitacio": "ID Tramitació"
					},
					"ORIGIN_OPTIONS": {
						"GAUDI": "GAUDI",
						"E_SPRIU": "e-SPRIU",
						"DALI": "DALI",
						"PADOCT": "PADOCT",
						"MIRO": "MIRO"
					}
				}
			},
			"DETAIL": {
				"PAGE_TITLE": "Detall de la gestió",
				"CARD": {
					"TITLE": "Dades de la gestió",
					"FIELDS": {
						"id": "ID Gestió Tracking",
						"idGestio": "ID Gestió",
						"idTramitacio": "ID Tramitació",
						"idTabTable": "ID Pestanyes",
						"estat": "Estat",
						"presentadorNom": "Nom Presentador",
						"presentadorNif": "NIF Presentador",
						"subjectePassiuNom": "Nom SP",
						"subjectePassiuNif": "NIF SP",
						"origen": "Origen",
						"tramit": "Tipus",
						"dadesAddicionals": "Dades Addicionals",
						"dataModificacio": "Data Modificació",
						"tipusActe": "Tipus Acte",
						"impost": "Impost",
						"import": "Import",
						"dataNotificacio": "Data Notificació"
					}
				},
				"STATE_HISTORY_TABLE": {
					"TITLE": "Historial d' estats",
					"COLUMNS": {
						"date": "Data",
						"state": "Estat"
					}
				},
				"TABS": {
					"ACTS": "Actes",
					"DOCUMENTS": "Documents",
					"PAYMENTS": "Pagaments",
					"PRESENTATIONS": "Presentacions",
					"TRAMITACIONS": "Tramitacions",
					"ERRORS": "Errors",
					"INTERVENERS": "Intervinents",
					"COMPLEMENTARY": "Complementària"
				}
			}
		},
		"MODULE_BIOMETRIC_SIGNATURE": {
			"PAGE_TITLE": "Dades signatura biomètrica",
			"TABS": {
				"ACTIVES": "Actives",
				"HISTORIC": "Històric"
			},
			"ENUMS": {
				"estat_peticio": {
					"GENERAT": "Generat",
					"ENVIAT": "Enviat",
					"SIGNAT": "Signat",
					"ERROR_PETICIO": "Error petició",
					"ERROR_NOTIFICACIO": "Error notificació",
					"ERROR_REPOSITORI_DOCUMENTAL": "Error repositori documental",
					"ERROR_HISTORIFICACIO": "Error historificació",
					"CANCELAT": "Cancelat",
					"CADUCAT": "Caducat",
					"REBUTJAT": "Rebutjat",
					"PENDENT": "Pendent",
					"NOTIFICAT": "Notificat"
				},
				"aplicacio": {
					"GAUDI": "GAUDI",
					"SRC": "SRC",
					"PORTAL": "PORTAL"
				},
				"tipusSignatura": {
					"EMAILANDSMS": "EMAILANDSMS",
					"BIO": "BIO",
					"MOBILE": "MOBILE",
					"SMARTCARD": "SMARTCARD",
					"STAMP": "STAMP"
				}
			},
			"TABLE": {
				"EMPTY_MSG": "Cap resultat coincideix amb els vostres criteris de cerca. Torneu-ho a provar.",
				"COLUMNS": {
					"id": "ID Peticio",
					"class": "Class",
					"anchor": "Anchor",
					"apiKey": "Api Key",
					"aplicacio": "Aplicació",
					"data_actualitzacio": "Data Actualització",
					"data_creacio": "Data Creació",
					"docGui": "docGui",
					"errors": "Errors",
					"stateRow": "Estat Petició",
					"identificador_signant": "Identificació Signant",
					"metadades": "Metadades",
					"nom_dispositiu": "Nom Dispositiu",
					"nom_document": "Nom Document",
					"nom_signant": "Nom Signant",
					"reintents": "Reintents",
					"repositori_documental": "Repositori Documental",
					"tipus_identificador_signant": "Tipus Identificador Signant",
					"tipus_signatura": "Tipus de Signatura",
					"id_vid_signer": "ID Vid Signer",
					"id_repositori_documental": "ID Repositori Documental"
				},
				"ROWS": {
					"TRAMIT": {
						"AVISOS": "Avisos",
						"IIIMA": "IIIMA",
						"ITPAJ": "ITPAJ",
						"IGEC": "IGEC",
						"DONACIONS": "Donacions",
						"IANP": "IANP",
						"ISBEE": "ISBEE",
						"ASSEGURANCES": "Assegurances",
						"RECURS": "Recurs",
						"RECURS_REPOSICIO_CO2": "Recurs CO2",
						"REA": "REA",
						"DOMICILIACIO": "Domiciliació",
						"ALLEGACIO_CO2": "Al·legacions CO2",
						"EXPEDIENT_CSV": "Expedient CSV",
						"CANVI_ADRECA": "Canvi Adreça",
						"ALTA_NE": "Alta not. electròniques",
						"MOD_NE": "Mod not. electròniques",
						"DEVOLUCIO_INGRESSOS": "Devolució Ingressos",
						"SIGNATURA_INSPECCIO": "Signatura Inspecció",
						"APORTAR_DOCUMENTACIO": "Aportar documentació"
					}
				},
				"FILTER": {
					"TITLE": "Filtres de cerca",
					"FIELDS": {
						"id": "Id petició",
						"aplicacio": "Aplicació",
						"data_actualitzacio_from": "Data actualització inici",
						"data_actualitzacio_to": "Data actualització final",
						"data_creacio_from": "Data creació inici",
						"data_creacio_to": "Data creació final",
						"estat_peticio": "Estat petició",
						"identificador_signant": "Identificació signant",
						"tipus_identificador_signant": "Tipus identificador signant",
						"tipus_signatura": "Tipus signatura",
						"opcions": "Opcions",
						"nomesErronis": "Només erronis",
						"metadades": "Metadades",
						"data_creacio_error": "Data creació error",
						"data_creacio": "Data creació error",
						"error_missatge": "Descripció error",
						"errors": "Errors"
					},
					"ORIGIN_OPTIONS": {
						"GAUDI": "GAUDI",
						"E_SPRIU": "e-SPRIU",
						"DALI": "DALI",
						"PADOCT": "PADOCT",
						"MIRO": "MIRO"
					}
				}
			},
			"DETAIL": {
				"PAGE_TITLE": "Detall de la signatura biomètrica",
				"RESET_BUTTON": "Reiniciar intents",
				"CARD": {
					"TITLE": "Dades de la signatura biomètrica",
					"FIELDS": {
						"id": "ID Peticio",
						"class": "Class",
						"anchor": "Anchor",
						"apiKey": "Api Key",
						"aplicacio": "Aplicació",
						"data_actualitzacio": "Data Actualització",
						"data_creacio": "Data Creació",
						"docGui": "docGui",
						"estat_peticio": "Estat Petició",
						"identificador_signant": "Identificació Signant",
						"nom_dispositiu": "Nom Dispositiu",
						"nom_document": "Nom Document",
						"nom_signant": "Nom Signant",
						"reintents": "Reintents",
						"repositori_documental": "Repositori Documental",
						"tipus_identificador_signant": "Tipus Identificador Signant",
						"tipus_signatura": "Tipus de Signatura",
						"id_vid_signer": "ID Vid Signer",
						"id_repositori_documental": "ID Repositori Documental",
						"metadades": "Metadades",
						"errors": "Errors",
						"metadades_value": "Veure metadades",
						"errors_value": "Veure errors"
					}
				},
				"STATE_HISTORY_TABLE": {
					"TITLE": "Historial d' estats",
					"COLUMNS": {
						"date": "Data",
						"state": "Estat"
					}
				},
				"TABS": {
					"ACTS": "Actes",
					"DOCUMENTS": "Documents",
					"PAYMENTS": "Pagaments",
					"PRESENTATIONS": "Presentacions",
					"TRAMITACIONS": "Tramitacions",
					"ERRORS": "Errors",
					"INTERVENERS": "Intervinents",
					"COMPLEMENTARY": "Complementària"
				}
			}
		}
	}
}
