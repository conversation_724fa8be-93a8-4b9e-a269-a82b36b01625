apiVersion: v1
kind: DeploymentConfig
metadata:
  name: pt-portal-tracking-gw
  labels:
    name: pt-portal-tracking-gw
    app: pt-app
    type: gateway 
spec:
  selector:
    name: pt-portal-tracking-gw
    app: pt-app
  strategy:
    type: Rolling
    resources:
      limits:
        cpu: 0
        memory: 0
      requests:
        cpu: 0
        memory: 0
  replicas: 2
  template:
    metadata:
      labels:
        name: pt-portal-tracking-gw
        app: pt-app
        type: gateway
      annotations:
        sidecar.istio.io/inject: 'true'
        sidecar.istio.io/proxyCPU: 175m
        sidecar.istio.io/proxyCPULimit: 175m
        sidecar.istio.io/proxyMemory: 350Mi
        sidecar.istio.io/proxyMemoryLimit: 350Mi
    spec:
      volumes:
        - name: html-volume
          persistentVolumeClaim:
            claimName: shared-pvc
        - name: gicar-metadata
          configMap:
            name: pt-portal-tracking-gw-config
            items:
            - key: idp-metadata
              path: idp-metadata.xml
      containers:
      - name: pt-portal-tracking-gw
        image: 'image-registry.openshift-image-registry.svc:5000/71024-0205-ptr-pre-inter/pt-portal-tracking-gw:0.183.0-snapshot'
        env:
        - name: CLIENT_APP_SCHEME
          value: https
        - name: CLIENT_APP_HOSTNAME
          value: preproduccio.admin-seu.atc.intranet.gencat.cat
        - name: SHIBBOLETH_RESPONDER_PATH
          value: /Shibboleth.sso
        - name: ENTITY_ID_SP
          value: 'https://preproduccio.admin-seu.atc.intranet.gencat.cat'
        - name: ENTITY_ID_IDP
          value: 'https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad'
        - name: CERTIFICATE_NAME
          value: SEUATC
        - name: BASE_SERVICE_DOMAIN
          value: '.71024-0205-ptr-pre-inter.svc.cluster.local'
        ports:
        - containerPort: 1080
          protocol: TCP
        resources:
          limits:
              cpu: 200m
              memory: 400Mi
          requests:
              cpu: 200m
              memory: 400Mi
        imagePullPolicy: Always
        volumeMounts:
          - name: html-volume
            mountPath: "/www"
          - name: gicar-metadata
            mountPath: /gicar
  test: false
