import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { LazyLoadEvent } from 'primeng/api';
import {
	PtTableColumn, PtValidations, iCommonError
} from 'pt-ui-components-mf-lib';
import { AutoliquidacionsV2EndpointsService } from '../../services/autoliquidacions-v2-endpoints.service';

import { AutoliquidacionsV2Service } from '../../services/autoliquidacions-v2.service';
import { DatePipe } from '@angular/common';
import { SelfAssessmentV2Request, SelfAssessmentV2Row, ISelfAssessmentV2Filter, ISelfAssessmentV2, AUTOLIQUIDACIONS_V2_FILTER_STORAGE, AUTOLIQUIDACIONS_V2_DETAIL_STORAGE, CanviEstat, States } from '../../models/autoliquidacions-v2.model';
import { SelfAssessmentV2Response, SelfAssessmentV2ResponseContent } from '../../models/autoliquidacions-v2-endpoints.model';
import { AppRoutes, SelfAssessmentV2Routes, StatesIcons, TaxesRoutes } from 'src/app/core/models/config.model';
import { IDetailData, TypesDeclaration } from '../autoliquidacions-v2-detail/models/autoliquidacions-v2-detail.model';
import { FilterModalInput } from '../../../../shared/modal-filter/models/modal-filter.model';
import { EXPORT_COLUMNS_DATA, ExportStateChangeRow, ExportTempData, FILTER_FORM_DATA, FilterFormFields, FilterFormValue, TableColumns, TableSortFields } from './models/autoliquidacions-v2-table.model';
import { ModalFilterComponent } from '../../../../shared/modal-filter';
import { TableExportService } from 'src/app/core/services/table-export.service';
import { PtTableComponent } from 'pt-ui-components-mf-lib/lib/components/table/table.component';
import { CurrencyPipe } from '@angular/common';

@Component({
	selector: 'app-resolucions-table',
	templateUrl: './autoliquidacions-v2-table.component.html',
	providers: [CurrencyPipe]
})
export class AutoliquidacionsV2TableComponent implements OnInit {
	
	@ViewChild('dt', { static: true }) private readonly table: PtTableComponent;

	filterValue: Partial<ISelfAssessmentV2Filter>;

	// Table
	searchColumns: PtTableColumn[] = [];
	searchRows: SelfAssessmentV2Row[] = [];

	// Other
	options: LazyLoadEvent;
	paginationTotal: number = 0;

	private _unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private endpointsService: AutoliquidacionsV2EndpointsService,
		private router: Router,
		private autoliquidacionsV2Service: AutoliquidacionsV2Service,
		private exportService: TableExportService,
		private currencyPipe: CurrencyPipe
	) { }

	ngOnInit(): void {
		this.searchColumns = this.setTableColumns();		
		this.filterValue = this.setFilterValue();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	private setTableColumns = (): PtTableColumn[] => [
		{
			id: TableColumns.numJustificant, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.numJustificant",
			isResizable: true, isSortable: true, width: "14%"
		},
		{
			id: TableColumns.stateRow, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.estat",
			isResizable: true, isSortable: true, template: 'link', width: "14%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.presentacioEstat, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.presentacioEstat",
			isResizable: true, isSortable: true, template: 'link', width: "12%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.pagamentEstat, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.pagamentEstat",
			isResizable: true, isSortable: true, template: 'link', width: "12%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.impost, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.impost",
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.tipus, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.tipus",
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.nomPresentador, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nomPresentador",
			isResizable: true, isSortable: true, isToggled: true, width: "17%"
		},
		{
			id: TableColumns.nifPresentador, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nifPresentador",
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.nomSubjectePassiu, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nomSubjectePassiu",
			isResizable: true, isSortable: true, isToggled: true, width: "17%"
		},
		{
			id: TableColumns.nifSubjectePassiu, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nifSubjectePassiu",
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.nomTitular, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nomTitular",
			isResizable: true, isSortable: true, isToggled: true, width: "17%"
		},
		{
			id: TableColumns.nifTitular, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nifTitular",
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.model, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.model",
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.idTramitacio, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.idTramitacio",
			isResizable: true, isSortable: true, isToggled: true, width: "12%"
		},
		{
			id: TableColumns.idAgrupacio, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.idAgrupacio",
			isResizable: true, isSortable: true, isToggled: true, width: "12%"
		},
		{
			id: TableColumns.idTramitacioPagament, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.idTramitacioPagament",
			isResizable: true, isSortable: true, isToggled: true, width: "12%"
		},
		{
			id: TableColumns.totalIngressar, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.totalIngressar",
			isResizable: true, isSortable: true, width: "12%"
		},
		{
			id: TableColumns.mui, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.mui",
			isResizable: true, isSortable: true, width: "12%"
		},
		{
			id: TableColumns.dataModificacio, label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.dataAlta",
			isResizable: true, isSortable: true, width: "15%", template: "date",
			options: { dateFormat: 'dd/MM/yyyy HH:mm' }
		},
		{
			id: TableColumns.tableActions, label: null, isResizable: false, isSortable: false,
			template: "buttons", width: '4%'
		}
	];

	private setFilterValue():Partial<ISelfAssessmentV2Filter> {
		const data: ISelfAssessmentV2Filter = this.autoliquidacionsV2Service.dataStorageService.getItem(AUTOLIQUIDACIONS_V2_FILTER_STORAGE);

		return new FilterFormValue(data);
	}
	
	openFilterModal() {
		// Open delete modal
		const modalRef = this.autoliquidacionsV2Service.modalService.open(
			ModalFilterComponent,
			{ size: 'xl', windowClass: '' }
		);
		
		const modalInput: FilterModalInput = this.setModalInput();
		modalRef.componentInstance.modalInput = modalInput;

		modalRef.componentInstance.modalOutput.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(filterFormValue: ISelfAssessmentV2Filter) => {
				this.filterValue = filterFormValue;

				this.table.table.reset();
			}
		);
	}
	
	private setModalInput (): FilterModalInput {
		return {
			formValue: this.filterValue,
			formResetValue: new FilterFormValue(),
			formFieldsData: FILTER_FORM_DATA,
			formGroupValidations: [
				PtValidations.equalsOrLessThan(FilterFormFields.dateToForm, FilterFormFields.dateFromForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan'),
				PtValidations.equalsOrGreaterThan(FilterFormFields.dateFromForm, FilterFormFields.dateToForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan'),
			]
		}
	}

	/**
	 * Search
	 * @description Search resolucions by criteria
	 */
	search = (event: LazyLoadEvent): void => {
		// Reset previous data
		this.searchRows = [];
		this.paginationTotal = 0;
		
		this.setOptionsEvent(event);
		
		this.autoliquidacionsV2Service.dataStorageService.setItem(AUTOLIQUIDACIONS_V2_FILTER_STORAGE, this.filterValue);

		const request: SelfAssessmentV2Request = new SelfAssessmentV2Request(this.filterValue, event)
		this.endpointsService.getSelfAssessmentList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: SelfAssessmentV2Response) => {
				if (response?.content) {
					this.setTableRows(response.content);
				}
			}
		);
	}
	
	private setOptionsEvent(event: LazyLoadEvent) {
		// Request
		if (!event.sortField){
			event.sortField = TableColumns.dataModificacio;
			event.sortOrder = -1;
		}

		if (TableSortFields[event.sortField]){
			event.sortField = TableSortFields[event.sortField];
		}

		this.options = event;
	}

	/**
	 * Table rows
	 * @description Map the table rows
	 */
	private setTableRows(response: SelfAssessmentV2ResponseContent) {
		this.paginationTotal = response.total;
		
		this.searchRows = response.results?.map(
			(autoliquidacio: ISelfAssessmentV2, id: number) => {
				const stateIcon = this.autoliquidacionsV2Service.getStateIcon(
					autoliquidacio?.estat,
					'self-assessment'
				);
				const paymentStateIcon =
					this.autoliquidacionsV2Service.getStateIcon(
						autoliquidacio?.pagamentEstat,
						'self-assessment'
					);
				const presentationStateIcon =
					this.autoliquidacionsV2Service.getStateIcon(
						autoliquidacio?.presentacioEstat,
						'self-assessment'
					);

				// Map autoliquidacio entity into the autoliquidacio table row
				return Object.assign(
					{},
					// Default autoliquidacio entity
					autoliquidacio,
					// Table row model
					{
						stateRow: {
							id: `button${autoliquidacio.idTramit}`,
							icon: stateIcon,
							iconPos: 'left',
							label: autoliquidacio.estat
								? this.autoliquidacionsV2Service.translateService.instant(
										`MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES.${autoliquidacio.estat}`
									)
								: '',
							...(autoliquidacio?.errors.length && stateIcon === StatesIcons.ERROR && {
								clickFn: this.showDetailError.bind(
									this,
									autoliquidacio?.errors
								)}
							),
							disabled: !this.isStateError(stateIcon) || !autoliquidacio?.errors.length,
						},
						tableActions: [
							{
								id: `detail${autoliquidacio.idTramit}`,
								icon: 'sli2-eye',
								class: 'p-button-text',
								componentType: 'button',
								clickFn: this.navigateToDetail.bind(
									this,
									autoliquidacio,
									id
								),
							},
						],
						[TableColumns.presentacioEstat]: {
							id: `button${autoliquidacio.idTramitacio}`,
							icon: presentationStateIcon,
							iconPos: 'left',
							label: autoliquidacio.presentacioEstat
								? this.autoliquidacionsV2Service.translateService.instant(
										`MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES.${autoliquidacio.presentacioEstat}`
									)
								: '',
							disabled: true,
						},
						[TableColumns.pagamentEstat]: {
							id: `button${autoliquidacio.idTramitacioPagament}`,
							icon: paymentStateIcon,
							iconPos: 'left',
							label: autoliquidacio.pagamentEstat
								? this.autoliquidacionsV2Service.translateService.instant(
										`MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES.${autoliquidacio.pagamentEstat}`
									)
								: '',
							disabled: true,
						},
						[TableColumns.totalIngressar]: this.formatTotalIngressar(autoliquidacio.totalIngressar, autoliquidacio.tipus),
					}
				);
			}
		);
	}

	private showDetailError = (errors?: iCommonError[]) => this.autoliquidacionsV2Service.showErrors(errors);

	private isStateError = (icon: string): boolean => icon === StatesIcons.ERROR;

	private navigateToDetail (row: SelfAssessmentV2Row,id: number) {
		const detailData: IDetailData = {
			filterValue : this.filterValue,
			totalRows: this.paginationTotal,
			options: this.options,
			index: id + this.options?.first,
			idSelfAssessment: row?.idAutoliquidacio
		}

		this.autoliquidacionsV2Service.dataStorageService.setItem(AUTOLIQUIDACIONS_V2_DETAIL_STORAGE, detailData);

		this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V2,'/',SelfAssessmentV2Routes.DETAIL), row.idAutoliquidacio]);
	}

	export = () : void =>  {
		const options = {...this.options,first:0, rows: 500}
		const request: SelfAssessmentV2Request = new SelfAssessmentV2Request(this.filterValue, options)
		this.endpointsService.getSelfAssessmentList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: SelfAssessmentV2Response) => {
				if (response?.content?.results?.length) {
					var datePipe = new DatePipe('en-US');
					let date = datePipe.transform(new Date(), 'yyyy-MM-dd_HH:mm');
					const responseList = response?.content.results.map(data => {
						return {
							...data,
							...(this.setTempData(data))
						}
					})
					
					this.exportService.export2Excel(responseList, EXPORT_COLUMNS_DATA, "Export_"+ date, 'Tributs');
				}
			}
		);
	}

	private setTempData(data: ISelfAssessmentV2):ExportTempData | null {
		const allowModels = ['600','651','652'];
		
		if(!allowModels.includes(data.model)) return null;
		
		const stateChangesList = this.getStateChangesList(data.canviEstat,new Date(data.dataAlta));
		
 		return {
			tempsValidacio: this.getTempValue(stateChangesList,States.ESBORRANY_VALIDAT,States.GENERAT),
  		tempsAgrupacio: this.getTempValue(stateChangesList,States.ESBORRANY_AGRUPAT,States.ESBORRANY_AGRUPANT),
			tempsPresentacio: this.getTempValue(stateChangesList,States.PRESENTAT,States.PRESENTANT),
			tempsPagament: this.getTempValue(stateChangesList,States.PAGAT,States.PAGANT),
		}
	}

	private getStateChangesList(stateChanges: CanviEstat[],firstDate: Date): ExportStateChangeRow[] {
		const firstRow: ExportStateChangeRow = {
			currentState: stateChanges[0]?.estatAnterior,
			date: firstDate
		}
		
		const formatedStateChangesList = stateChanges.map(state => {
			return {
				currentState: state.estat,
				date: state.dataCanvi
			}
		}) || [];

		return [firstRow,...formatedStateChangesList]
	}

	private getTempValue(stateList: ExportStateChangeRow[],upperState: States, lowerState: States): string {
		const upperRow = stateList.find(row => row.currentState === upperState);
		const lowerRow = stateList.find(row => row.currentState === lowerState);

		if(upperRow && lowerRow) {
			const upperDate = new Date(upperRow.date);
			const lowerDate = new Date(lowerRow.date);
			return Math.abs((upperDate.getTime() - lowerDate.getTime())/1000).toFixed(3).replace('.',',');
		}

		return "";
	}

	private formatTotalIngressar(totalIngressar: number, tipus: string): string {
		if (tipus === TypesDeclaration.DECINF) {
			return '';
		}
		return this.currencyPipe.transform(totalIngressar, 'EUR', 'symbol-narrow', '1.2-2', 'ca-ES');
	}
}
